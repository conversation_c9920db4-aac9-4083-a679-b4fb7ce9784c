# 系统架构

## 高层架构

```
┌─────────────────────────────────────────────────────────────┐
│                        用户界面层                             │
│  ┌─────────────┐  ┌──────────────┐  ┌──────────────────┐  │
│  │  CLI 入口   │  │     REPL     │  │  React/Ink UI    │  │
│  │  (cli.tsx)  │  │  (REPL.tsx)  │  │     组件         │  │
│  └─────────────┘  └──────────────┘  └──────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      命令与控制层                             │
│  ┌─────────────┐  ┌──────────────┐  ┌──────────────────┐  │
│  │    命令      │  │   查询引擎    │  │    上下文管理     │  │
│  │  (斜杠 /)   │  │              │  │                  │  │
│  └─────────────┘  └──────────────┘  └──────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                       工具执行层                              │
│  ┌─────────────┐  ┌──────────────┐  ┌──────────────────┐  │
│  │  文件工具    │  │   系统工具    │  │    AI 工具       │  │
│  │ (读/写/编辑) │  │ (Bash/Grep)  │  │  (Task/Think)    │  │
│  └─────────────┘  └──────────────┘  └──────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      服务集成层                              │
│  ┌─────────────┐  ┌──────────────┐  ┌──────────────────┐  │
│  │ AI 提供商   │  │  MCP 服务器   │  │   外部 API       │  │
│  │(Claude/GPT) │  │ (stdio/SSE)  │  │  (Git/Sentry)    │  │
│  └─────────────┘  └──────────────┘  └──────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                       基础设施层                              │
│  ┌─────────────┐  ┌──────────────┐  ┌──────────────────┐  │
│  │  配置管理    │  │   权限系统    │  │   日志与错误      │  │
│  │             │  │              │  │     处理         │  │
│  └─────────────┘  └──────────────┘  └──────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 组件关系

### 数据流架构

```
用户输入 → 命令解析器 → 路由决策
                          ↓
                ┌─────────────────────┐
                │    是斜杠命令吗？     │
                └─────────────────────┘
                          ↓
          是 ←────────────┼────────────→ 否
           ↓                              ↓
    ┌──────────────┐              ┌──────────────┐
    │  命令处理器   │              │   AI 查询     │
    └──────────────┘              └──────────────┘
           ↓                              ↓
    ┌──────────────┐              ┌──────────────┐
    │     执行     │              │   工具选择    │
    └──────────────┘              └──────────────┘
           ↓                              ↓
    ┌──────────────┐              ┌──────────────┐
    │   返回结果   │              │   工具执行    │
    └──────────────┘              └──────────────┘
           ↓                              ↓
           └──────────┬──────────────────┘
                      ↓
              ┌──────────────┐
              │   渲染响应    │
              └──────────────┘
```

### 模块依赖图

```
cli.tsx (入口点)
    ├── REPL.tsx (交互模式)
    │   ├── PromptInput.tsx
    │   ├── MessageResponse.tsx
    │   └── query.ts (AI 编排)
    │       ├── tools.ts (工具注册)
    │       │   ├── BashTool
    │       │   ├── FileEditTool
    │       │   ├── GrepTool
    │       │   └── [其他工具]
    │       ├── permissions.ts
    │       └── context.ts
    ├── commands.ts (命令系统)
    │   ├── 内置命令
    │   ├── MCP 命令
    │   └── 自定义命令
    └── services/
        ├── claude.ts
        ├── openai.ts
        ├── mcpClient.ts
        └── statsig.ts
```

## 核心模块交互

### 1. 对话流程

```
用户提示
    ↓
上下文注入 (AGENTS.md, git 状态等)
    ↓
模型选择 (基于上下文大小)
    ↓
带工具的 API 请求
    ↓
流式响应
    ↓
工具使用检测
    ↓
权限检查
    ↓
工具执行
    ↓
结果集成
    ↓
继续或完成
```

### 2. 工具执行管道

```
AI 请求工具使用
    ↓
工具验证 (Zod Schema)
    ↓
权限检查
    ├── 宽松模式 → 自动批准安全操作
    └── 安全模式 → 请求用户权限
    ↓
工具执行 (异步生成器)
    ├── 产生进度更新
    ├── 处理取消
    └── 返回结果
    ↓
为 AI 格式化结果
    ↓
继续对话
```

### 3. 配置级联

```
环境变量
    ↓
全局配置 (~/.claude/config.json)
    ↓
项目配置 (./.claude/config.json)
    ↓
运行时覆盖 (CLI 标志)
    ↓
有效配置
```

### 4. MCP 服务器集成

```
MCP 服务器配置
    ↓
服务器启动 (stdio/SSE)
    ↓
工具发现
    ↓
动态工具注册
    ↓
对话中可用的工具
    ↓
通过 MCP 协议执行工具
    ↓
结果转换
```

## 关键架构决策

### 1. 工具抽象
每个功能都是实现标准接口的工具：
```typescript
interface Tool {
  name: string
  description: string
  inputSchema: ZodSchema
  needsPermissions(): boolean
  call(): AsyncGenerator<ToolCallResult>
  renderResultForAssistant(): string
}
```

### 2. 流式架构
所有长时间运行的操作都使用异步生成器：
- 启用实时进度更新
- 支持任何时候取消
- 允许增量结果显示

### 3. 终端中的 React
使用 React 与 Ink 用于终端 UI：
- 组件可重用性
- 状态管理
- 复杂的交互式 UI
- 一致的样式

### 4. 权限层
多级权限系统：
- **工具级别**：每个工具声明权限需求
- **会话级别**：当前会话的临时权限
- **持久级别**：跨会话保存的权限
- **模式级别**：安全与宽松模式

### 5. 上下文管理
自动上下文注入：
- 项目文件 (AGENTS.md, CLAUDE.md)
- Git 状态和最近的提交
- 目录结构
- 先前的对话历史

## 模块通信模式

### 事件驱动更新
组件通过以下方式通信：
- React props 和回调
- 用于取消的中止信号
- 用于更新的进度生成器
- 用于横切关注点的事件发射器

### 服务集成
通过以下方式访问外部服务：
- 专用服务模块
- 统一错误处理
- 带退避的重试逻辑
- 流式响应处理

### 配置访问
通过以下方式访问配置：
- 单例配置管理器
- 首次访问时延迟加载
- 版本更改时自动迁移
- 加载时验证

## 安全架构

### 权限模型
```
请求 → 权限检查 → 决策
         ↓            ↓
    ┌──────────┐  ┌──────┐
    │ 检查缓存  │  │ 拒绝  │
    └──────────┘  └──────┘
         ↓
    ┌──────────┐
    │ 检查会话  │
    └──────────┘
         ↓
    ┌──────────┐
    │ 询问用户  │
    └──────────┘
         ↓
    ┌──────────┐
    │ 缓存决策  │
    └──────────┘
```

### 文件系统安全
- 基于目录的访问控制
- 路径遍历防护
- 符号链接解析
- 隐藏文件保护

### 命令执行安全
- 命令批准系统
- 环境变量清理
- 工作目录限制
- 输出大小限制

## 性能考虑

### 缓存策略
- 模型响应缓存在内存中
- 带新鲜度检查的文件读取缓存
- 每个会话的配置缓存
- MCP 工具发现缓存

### 延迟加载
- 首次使用时加载命令
- 按需启动 MCP 服务器
- 增量加载上下文
- 需要时加载重型依赖

### 流式优化
- 分块响应处理
- 增量渲染
- 部分结果显示
- 早期终止支持

## 扩展点

### 添加新工具
1. 创建扩展 Tool 的类
2. 实现所需方法
3. 在 tools.ts 中注册
4. 工具自动可用

### 添加 AI 提供商
1. 实现提供商接口
2. 添加到 ModelManager
3. 在模型配置文件中配置
4. 提供商可供使用

### 添加命令
1. 创建命令处理器
2. 在 commands.ts 中注册
3. 通过斜杠可用命令

### 添加 MCP 服务器
1. 配置服务器详细信息
2. 服务器工具自动发现
3. 工具在对话中可用

## 系统边界

### 内部边界
- 清晰的模块接口
- 依赖注入
- 服务抽象
- 工具隔离

### 外部边界
- API 速率限制
- 文件系统访问控制
- 网络请求超时
- 资源消耗限制

## 可扩展性考虑

### 水平可扩展性
- 无状态命令执行
- 独立的工具操作
- 并行工具执行（安全时）
- 分布式 MCP 服务器

### 垂直可扩展性
- 大响应的流式传输
- 分块文件处理
- 增量上下文加载
- 内存高效缓存

这种架构为 AI 驱动的开发助手提供了强大、可扩展的基础，同时将安全性、性能和用户体验作为主要关注点。